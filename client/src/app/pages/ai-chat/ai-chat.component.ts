import { CommonModule } from '@angular/common'
import { ChangeDetectionStrategy, Component, computed, inject, OnInit, signal } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { AiChatService } from '../../services/ai-chat.service'
import { ChatMainComponent } from './components/chat-main/chat-main.component'
import { ChatSidebarComponent } from './components/chat-sidebar/chat-sidebar.component'
import { SourcePanelComponent } from './components/source-panel/source-panel.component'

export interface ChatMessage {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  sources?: ChatSource[];
  isTyping?: boolean;
  isTypewriting?: boolean;
  fullContent?: string;
}

export interface ChatSource {
  id: string;
  title: string;
  content: string;
  url?: string;
  highlightedText?: string;
}

export interface Chat {
  id: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  isPinned: boolean;
  messages: ChatMessage[];
}

@Component({
  selector: 'app-ai-chat',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ChatSidebarComponent,
    ChatMainComponent,
    SourcePanelComponent
  ],
  templateUrl: './ai-chat.component.html',
  styleUrl: './ai-chat.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AiChatComponent implements OnInit {
  private aiChatService = inject(AiChatService);

  chats = signal<Chat[]>([]);
  currentChatId = signal<string | null>(null);
  selectedSource = signal<ChatSource | null>(null);
  isSourcePanelOpen = signal<boolean>(false);
  isSidebarOpen = signal<boolean>(true);
  isLoading = signal<boolean>(false);

  currentChat = computed(() => {
    const chatId = this.currentChatId();
    const chats = this.chats();
    const foundChat = chats.find(chat => chat.id === chatId) || null;
    return foundChat;
  });

  ngOnInit() {
    this.loadChats();
  }

  loadChats() {
    this.aiChatService.getChats().subscribe(chats => {
      this.chats.set(chats);

      if (!this.currentChatId() && chats.length > 0) {
        this.currentChatId.set(chats[0].id);
      }
    });
  }

  onNewChat() {
    this.aiChatService.createNewChat().subscribe((newChat) => {
      if (!newChat.messages) {
        newChat.messages = [];
      }
      this.chats.update(chats => [newChat, ...chats]);
      this.currentChatId.set(newChat.id);
    });
  }

  onChatSelect(chatId: string) {
    this.currentChatId.set(chatId);

    this.aiChatService.getChatMessages(chatId).subscribe({
      next: (messages) => {
        this.chats.update(chats =>
          chats.map(chat =>
            chat.id === chatId ? { ...chat, messages: messages || [] } : chat
          )
        );
      },
      error: () => {
        console.error('Error loading chat messages');
        this.chats.update(chats =>
          chats.map(chat =>
            chat.id === chatId ? { ...chat, messages: [] } : chat
          )
        );
      }
    });
  }

  onChatRename(chatId: string, newTitle: string) {
    this.aiChatService.updateChat(chatId, { title: newTitle }).subscribe((updatedChat) => {
      this.chats.update(chats =>
        chats.map(chat =>
          chat.id === chatId ? updatedChat : chat
        )
      );
    });
  }

  onChatDelete(chatId: string) {
    this.aiChatService.deleteChat(chatId).subscribe(() => {
      this.chats.update(chats => chats.filter(chat => chat.id !== chatId));
      if (this.currentChatId() === chatId) {
        const remainingChats = this.chats();
        if (remainingChats.length > 0) {
          this.onChatSelect(remainingChats[0].id);
        } else {
          this.currentChatId.set(null);
        }
      }
    });
  }

  onChatPin(chatId: string) {
    const chat = this.chats().find(c => c.id === chatId);
    if (!chat) return;

    this.aiChatService.updateChat(chatId, { isPinned: !chat.isPinned }).subscribe((updatedChat) => {
      this.chats.update(chats =>
        chats.map(chat =>
          chat.id === chatId ? updatedChat : chat
        )
      );
    });
  }

  onSendMessage(content: string) {
    const currentChat = this.currentChat();
    if (!currentChat) return;

    const userMessage: ChatMessage = {
      id: this.generateId(),
      content: content,
      role: 'user',
      timestamp: new Date()
    };

    const typingMessage: ChatMessage = {
      id: this.generateId(),
      content: '',
      role: 'assistant',
      timestamp: new Date(),
      isTyping: true
    };

    const currentMessages = [...(currentChat.messages || []), userMessage, typingMessage];
    this.updateChatMessages(currentChat.id, currentMessages);
    this.isLoading.set(true);

    this.aiChatService.sendMessage(currentChat.id, content).subscribe({
      next: (response: any) => {
        const updatedChat = this.currentChat();
        if (updatedChat) {
          const messagesWithoutTyping = (updatedChat.messages || []).filter(m => !m.isTyping);

          const fullContent = response.aiResponse?.message || response.assistantMessage?.content || '';
          const aiMessage: ChatMessage = {
            id: this.generateId(),
            content: '',
            role: 'assistant',
            timestamp: new Date(),
            sources: response.aiResponse?.sources || response.sources || [],
            isTypewriting: true,
            fullContent: fullContent
          };

          this.updateChatMessages(currentChat.id, [...messagesWithoutTyping, aiMessage]);
        }
        this.isLoading.set(false);
      },
      error: () => {
        console.error('Error sending message');
        const updatedChat = this.currentChat();
        if (updatedChat) {
          const messagesWithoutTyping = (updatedChat.messages || []).filter(m => !m.isTyping);
          this.updateChatMessages(currentChat.id, messagesWithoutTyping);
        }
        this.isLoading.set(false);
      }
    });
  }

  onSourceClick(source: ChatSource) {
    this.selectedSource.set(source);
    this.isSourcePanelOpen.set(true);
  }

  onSourcePanelClose() {
    this.isSourcePanelOpen.set(false);
    this.selectedSource.set(null);
  }

  onTypewritingComplete(messageId: string) {
    const currentChat = this.currentChat();
    if (!currentChat) return;

    this.chats.update(chats =>
      chats.map(chat => {
        if (chat.id === currentChat.id) {
          const updatedMessages = (chat.messages || []).map(message => {
            if (message.id === messageId && message.isTypewriting) {
              return {
                ...message,
                content: message.fullContent || message.content,
                isTypewriting: false,
                fullContent: undefined
              };
            }
            return message;
          });
          return { ...chat, messages: updatedMessages };
        }
        return chat;
      })
    );
  }

  onToggleSidebar() {
    this.isSidebarOpen.update(open => !open);
  }

  private updateChatMessages(chatId: string, messages: ChatMessage[]) {
    this.chats.update(chats => 
      chats.map(chat => 
        chat.id === chatId ? { ...chat, messages, updatedAt: new Date() } : chat
      )
    );
  }



  private generateId(): string {
    return Math.random().toString(36).substring(2, 11);
  }
}
