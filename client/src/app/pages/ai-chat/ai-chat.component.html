<div class="ai-chat-container">
  <!-- Left Sidebar - Chat History -->
  <app-chat-sidebar
    [class.sidebar-hidden]="!isSidebarOpen()"
    [chats]="chats()"
    [currentChatId]="currentChatId()"
    (newChat)="onNewChat()"
    (chatSelect)="onChatSelect($event)"
    (chatRename)="onChatRename($event.chatId, $event.newTitle)"
    (chatDelete)="onChatDelete($event)"
    (chatPin)="onChatPin($event)"
  ></app-chat-sidebar>

  <!-- Main Chat Area -->
  <div class="chat-main-wrapper">
    <!-- Mobile Sidebar Toggle -->
    <button 
      class="sidebar-toggle mobile-only"
      (click)="onToggleSidebar()"
      [class.sidebar-open]="isSidebarOpen()"
    >
      <span class="burger-line"></span>
      <span class="burger-line"></span>
      <span class="burger-line"></span>
    </button>

    <app-chat-main
      [chat]="currentChat()"
      [isLoading]="isLoading()"
      (sendMessage)="onSendMessage($event)"
      (sourceClick)="onSourceClick($event)"
      (typewritingComplete)="onTypewritingComplete($event)"
    ></app-chat-main>
  </div>

  <!-- Right Panel - Source Viewer -->
  <app-source-panel
    [class.panel-open]="isSourcePanelOpen()"
    [source]="selectedSource()"
    [isOpen]="isSourcePanelOpen()"
    (close)="onSourcePanelClose()"
  ></app-source-panel>

  <!-- Overlay for mobile -->
  @if (isSidebarOpen()) {
    <div class="mobile-overlay" (click)="onToggleSidebar()"></div>
  }
</div>
