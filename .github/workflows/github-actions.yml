name: Deploy
on:
  push:
    branches:
      - dev
jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'
          cache-dependency-path: admin/package-lock.json

      - name: Install deps & build admin
        run: |
          cd admin
          npm ci --force
          npm run build    

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.5.3
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Upload admin dist to server
        run: |
          scp -o StrictHostKeyChecking=no -P 822 -r admin/dist alexander@**************:/home/<USER>/dev.advayta.org/admin/    

      - name: Deploy to server
        run: |
          ssh -o StrictHostKeyChecking=no alexander@************** -p 822 << 'EOF'
          set -euo pipefail
    
          cd /home/<USER>/dev.advayta.org

          git fetch origin dev
          git reset --hard origin/dev
          
          export BUILD_TARGET=production
          
          podman-compose down || true
          podman-compose build
          podman-compose up -d
          
          podman image prune -f

          podman cp dev.advayta.org-client:/client/dist/client/. ./client/dist/
          
          EOF