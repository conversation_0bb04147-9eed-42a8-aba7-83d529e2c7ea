import { Chat } from '@/entity/Chat'
import { ChatMessage } from '@/entity/ChatMessage'
import { ChatSource } from '@/entity/ChatSource'
import { HttpModule } from '@nestjs/axios'
import { Module } from '@nestjs/common'
import { ConfigModule } from '@nestjs/config'
import { TypeOrmModule } from '@nestjs/typeorm'
import { AiChatController } from './ai-chat.controller'
import { AiChatService } from './ai-chat.service'

@Module({
    imports: [TypeOrmModule.forFeature([Chat, ChatMessage, ChatSource]), HttpModule, ConfigModule],
    controllers: [AiChatController],
    providers: [AiChatService],
})
export class AiChatModule {}
