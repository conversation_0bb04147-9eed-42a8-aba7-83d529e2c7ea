import { Chat } from '@/entity/Chat'
import { ChatMessage, MessageRole } from '@/entity/ChatMessage'
import { ChatSource } from '@/entity/ChatSource'
import { HttpService } from '@nestjs/axios'
import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common'
import { ConfigService } from '@nestjs/config'
import * as marked from 'marked'

@Injectable()
export class AiChatService {
    constructor(
        private readonly httpService: HttpService,
        private readonly configService: ConfigService
    ) {}

    async getChats(userId: number) {
        const chats = await Chat.find({
            where: { userId },
            relations: ['messages', 'messages.sources'],
            order: { updatedAt: 'DESC' }
        });

        return chats.map(chat => ({
            id: chat.id,
            title: chat.title,
            createdAt: chat.createdAt,
            updatedAt: chat.updatedAt,
            isPinned: chat.isPinned,
            messages: chat.messages.map(message => ({
                id: message.id,
                content: message.content,
                role: message.role,
                timestamp: message.timestamp,
                isTyping: message.isTyping,
                sources: message.sources || []
            }))
        }));
    }

    async createNewChat(userId: number) {
        const chat = Chat.create({
            title: 'Новый чат',
            userId,
            isPinned: false
        });

        return chat.save();
    }

    async updateChat(chatId: string, userId: number, updates: Partial<Chat>) {
        const chat = await Chat.findOne({
            where: { id: chatId, userId }
        });

        if (!chat) {
            throw new NotFoundException('Чат не найден');
        }

        Object.assign(chat, updates);
        return chat.save();
    }

    async deleteChat(chatId: string, userId: number) {
        const chat = await Chat.findOne({
            where: { id: chatId, userId }
        });

        if (!chat) {
            throw new NotFoundException('Чат не найден');
        }

        await chat.remove();
    }

    async getChatMessages(chatId: string, userId: number) {
        const chat = await Chat.findOne({
            where: { id: chatId, userId }
        });

        if (!chat) {
            throw new NotFoundException('Чат не найден');
        }

        const messages = await ChatMessage.find({
            where: { chatId },
            relations: ['sources'],
            order: { timestamp: 'ASC' }
        });

        return messages.map(message => ({
            id: message.id,
            content: message.content,
            role: message.role,
            timestamp: message.timestamp,
            isTyping: message.isTyping,
            sources: message.sources || []
        }));
    }

    async sendMessage(chatId: string, userId: number, content: string, userEmail: string) {
        const chat = await Chat.findOne({
            where: { id: chatId, userId }
        });

        if (!chat) {
            throw new NotFoundException('Чат не найден');
        }

        const userMessage = ChatMessage.create({
            chatId,
            content,
            role: MessageRole.USER,
            isTyping: false
        });

        await userMessage.save();

        try {
            const response = await this.httpService.axiosRef.post(
                this.configService.get('AI_CHAT_URL'),
                {
                    chatInput: content,
                    sessionId: chatId,
                    userEmail: userEmail
                },
                {
                    responseType: 'text'
                }
            );

            if (!response.data) {
                throw new Error('Ошибка при обращении к AI сервису');
            }

            const aiResponse = this.parseStreamingResponse(response.data);

            const responseContent = aiResponse.response || aiResponse.message || 'Ответ получен, но содержимое пустое.';
            const htmlContent = this.convertMarkdownToHtml(responseContent);

            const assistantMessage = ChatMessage.create({
                chatId,
                content: htmlContent,
                role: MessageRole.ASSISTANT,
                isTyping: false
            });

            const savedMessage = await assistantMessage.save();

            if (aiResponse.sources && Array.isArray(aiResponse.sources)) {
                const sources = aiResponse.sources.map((source: any) =>
                    ChatSource.create({
                        messageId: savedMessage.id,
                        title: source.title || '',
                        content: source.content || '',
                        url: source.url,
                        highlightedText: source.highlightedText
                    })
                );

                await Promise.all(sources.map((source: any) => source.save()));
            }

            if (chat.title === 'Новый чат' && content.length > 0) {
                chat.title = content.substring(0, 50) + (content.length > 50 ? '...' : '');
                await chat.save();
            }

            const messageWithSources = await ChatMessage.findOne({
                where: { id: savedMessage.id },
                relations: ['sources']
            });

            return {
                userMessage: {
                    id: userMessage.id,
                    content: userMessage.content,
                    role: userMessage.role,
                    timestamp: userMessage.timestamp,
                    isTyping: userMessage.isTyping,
                    sources: []
                },
                assistantMessage: {
                    id: messageWithSources.id,
                    content: messageWithSources.content,
                    role: messageWithSources.role,
                    timestamp: messageWithSources.timestamp,
                    isTyping: messageWithSources.isTyping,
                    sources: messageWithSources.sources || []
                },
                aiResponse: {
                    ...aiResponse,
                    response: htmlContent,
                    message: htmlContent
                }
            };

        } catch (error) {
            let errorContent = 'Извините, произошла ошибка при обработке вашего запроса.';

            const errorMessage = ChatMessage.create({
                chatId,
                content: errorContent,
                role: MessageRole.ASSISTANT,
                isTyping: false
            });

            const savedErrorMessage = await errorMessage.save();

            return {
                userMessage: {
                    id: userMessage.id,
                    content: userMessage.content,
                    role: userMessage.role,
                    timestamp: userMessage.timestamp,
                    isTyping: userMessage.isTyping,
                    sources: []
                },
                assistantMessage: {
                    id: savedErrorMessage.id,
                    content: savedErrorMessage.content,
                    role: savedErrorMessage.role,
                    timestamp: savedErrorMessage.timestamp,
                    isTyping: savedErrorMessage.isTyping,
                    sources: []
                },
                aiResponse: { error: true, message: errorContent }
            };
        }
    }

    private parseStreamingResponse(streamData: string) {
        const lines = streamData.split('\n').filter(line => line.trim());
        let fullContent = '';
        let sources: any[] = [];
        let metadata: any = {};

        for (const line of lines) {
            try {
                const parsed = JSON.parse(line);

                if (parsed.type === 'begin') {
                    metadata = parsed.metadata || {};
                } else if (parsed.type === 'item') {
                    fullContent += parsed.content || '';
                } else if (parsed.type === 'sources' && parsed.sources) {
                    sources = parsed.sources;
                } else if (parsed.type === 'end') {
                    break;
                }
            } catch (error) {
                continue;
            }
        }

        this.extractSourcesFromContent(fullContent, sources);

        return {
            response: fullContent,
            message: fullContent,
            sources: sources,
            metadata: metadata
        };
    }

    private extractSourcesFromContent(content: string, sources: any[]) {
        const linkRegex = /\[([^\]]+)\]\((https?:\/\/[^\)]+)\)/g;
        let match: RegExpExecArray | null;

        while ((match = linkRegex.exec(content)) !== null) {
            const title = match[1];
            const url = match[2];

            const existingSource = sources.find(s => s.url === url);
            if (!existingSource) {
                sources.push({
                    title: title,
                    content: title,
                    url: url,
                    highlightedText: null
                });
            }
        }
    }

    private convertMarkdownToHtml(markdownText: string) {
        if (!markdownText) return '';

        try {
            marked.setOptions({
                breaks: true,
                gfm: true
            });

            const html = marked.parse(markdownText);

            if (html instanceof Promise) {
                return `<p>${markdownText.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}</p>`;
            }

            return html;
        } catch (error) {
            return `<p>${markdownText.replace(/\n\n/g, '</p><p>').replace(/\n/g, '<br>')}</p>`;
        }
    }

    async fetchUrl(url: string) {
        try {
            const searchQuery = new URL(url).searchParams.get('q');

            const response = await this.httpService.axiosRef.get(url, {
                timeout: 10000,
                responseType: 'text'
            });

            if (!response.data) {
                throw new BadRequestException('Не удалось загрузить файл');
            }

            const urlParts = url.split('/');
            let filename = urlParts[urlParts.length - 1];

            filename = decodeURIComponent(filename);

            const title = filename.replace(/\.md$/i, '') || 'Документ';

            let content = this.convertMarkdownToHtml(response.data);

            if (searchQuery && searchQuery.trim()) {
                content = this.highlightSearchTerms(content, searchQuery);
            }

            return {
                success: true,
                data: {
                    url: url,
                    title: title,
                    content: content
                }
            };

        } catch (error) {
            if (error instanceof BadRequestException) {
                throw error;
            }
            throw new BadRequestException('Не удалось загрузить файл: ' + (error.message || 'Неизвестная ошибка'));
        }
    }

    private highlightSearchTerms(content: string, searchQuery: string) {
        if (!searchQuery || !searchQuery.trim()) {
            return content;
        }

        const trimmedQuery = searchQuery.trim();

        const escapedQuery = trimmedQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

        const regex = new RegExp(`(${escapedQuery})`, 'gi');

        const highlightedContent = content.replace(regex, (match) => {
            return `<span class="search-highlight">${match}</span>`;
        });

        return highlightedContent;
    }
}
