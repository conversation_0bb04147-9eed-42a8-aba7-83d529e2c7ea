<div class="panel">
  <dialog #dialog class="admin-modal">
    <div class="admin-modal-content">
      <div>{{message}}</div>
      <div class="admin-modal-footer">
        <button (click)="close(dialog)" class="btn btn-primary">ОК</button>
      </div>
    </div>
  </dialog>
  <button class="p-2 ml-auto block" (click)="goBack()">X</button>
  <form [formGroup]="categoryAddForm" class="space-y-5">
    <div>
      <label><input type="checkbox" class="form-checkbox" formControlName="active">Опубликовать</label>
    </div>
    <div>
      <label>Название категории</label>
      <div [ngClass]="{ 'has-error': categoryAddForm.get('title')?.invalid && categoryAddForm.get('title')?.touched }" class="flex items-center">
        <input formControlName="title" type="text" class="form-input" placeholder="Название категории"><span class="asterix">*</span>
        <span title="Название категории" class="cursor-pointer mx-2">
          &#9432;
        </span>
      </div>
    </div>
    <div>
      <label>Превью</label>
      <div [ngClass]="{ 'has-error': categoryAddForm.get('preview')?.invalid && categoryAddForm.get('preview')?.touched }" class="flex items-center">
        <input class="form-input"   accept="image/*"  type="file" (change)="uploadPreview($event)"><span class="asterix">*</span>
        <span title="Категория" class="cursor-pointer mx-2">
          &#9432;
        </span>
      </div>
      @if(categoryAddForm.value.preview) {
        <PhotoPreview [disableRemoveBtn]="true" [items]="[categoryAddForm.value.preview]" (onItemRemoved)="categoryAddForm.patchValue({preview: null})"></PhotoPreview>
      }
    </div>
    <div>
      <button [disabled]="categoryAddForm.invalid" (click)="categoryAdd()" class="btn btn-success">Сохранить</button>
    </div>
  </form>
</div>
